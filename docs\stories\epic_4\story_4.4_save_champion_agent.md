---
Status: Approved
Story:
  Sequence: 4.4
  Title: Save the Champion Agent for Backtesting and Live Trading
  Description: As a developer, I want to ensure the final, best-performing agent from the autonomous stage is saved correctly so that it can be used as the definitive model for backtesting and live trading.
Acceptance Criteria:
  1: At the end of the `run_autonomous_stage`, the single best-performing agent from the final generation is identified.
  2: Its complete state (model weights, architecture definition if dynamically generated by NAS, and best tuned hyperparameters) is saved to a file named `{symbol}_autonomous_final.pth`.
  3: The `run_backtest.py` script is modified to **exclusively** load and use the `{symbol}_autonomous_final.pth` model when running a backtest on a symbol that has completed the autonomous stage.
  4: The `run_live_bot.py` script is also modified to **exclusively** load and use the `{symbol}_autonomous_final.pth` model for live trading.
Tasks:
  - Task: Identify and save the champion agent
    Subtasks:
      - Subtask: In `run_autonomous_stage`, after the final generation, identify the agent with the highest fitness score.
      - Subtask: Implement a saving mechanism to store the agent's `state_dict`, its dynamically generated architecture definition, and its final tuned hyperparameters into a single `.pth` file.
  - Task: Modify `run_backtest.py` for exclusive loading
    Subtasks:
      - Subtask: Locate the model loading logic in `run_backtest.py`.
      - Subtask: Modify it to first check for `{symbol}_autonomous_final.pth`.
      - Subtask: If found, load and use ONLY this model, bypassing other model loading paths.
  - Task: Modify `run_live_bot.py` for exclusive loading
    Subtasks:
      - Subtask: Locate the model loading logic in `run_live_bot.py`.
      - Subtask: Modify it to first check for `{symbol}_autonomous_final.pth`.
      - Subtask: If found, load and use ONLY this model, bypassing other model loading paths.
Dev Notes:
Testing:
Dev Agent Record:
  - [ ] Task:
  - [ ] Subtask:
Change Log: