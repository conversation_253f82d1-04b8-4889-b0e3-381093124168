---
Status: Approved
Story:
  Sequence: 3.1
  Title: Implement the Neural Architecture Search (NAS) Framework
  Description: As a developer, I want to create a framework for Neural Architecture Search so that the agent can discover and evolve its own model architectures.
Acceptance Criteria:
  1: A new directory `src/nas/` is created.
  2: A file `src/nas/search_space.py` is created to define the "Lego box" of possible layers and operations.
  3: A file `src/nas/search_controller.py` is created to house the search algorithm (e.g., DARTS or an Evolutionary Algorithm).
  4: The `search_controller` can take a population of agent architectures and generate a new, evolved population based on fitness scores, interacting with the `CoreTransformer` module to define and modify architectures.
  5: A unit test is created to verify that the NAS controller can generate valid new architectures from the defined search space.
Tasks:
  - Task: Create `src/nas/` directory and `search_space.py`
    Subtasks:
      - Subtask: Create the directory structure
      - Subtask: Define a dictionary or class in `search_space.py` to hold available layers (e.g., `nn.Linear`, `nn.<PERSON><PERSON><PERSON>`, `nn.<PERSON>er<PERSON>orm`, `CoreTransformer` blocks) and their configurable parameters.
  - Task: Implement `search_controller.py`
    Subtasks:
      - Subtask: Define `NASController` class.
      - Subtask: Implement a method (e.g., `generate_architecture`) that samples from the `search_space` to create a new architecture definition (e.g., a list of layer configurations).
      - Subtask: Implement a method (e.g., `evolve_population`) that takes a list of agent fitnesses and uses a search algorithm (e.g., simple genetic algorithm: selection, crossover, mutation) to propose new architectures.
      - Subtask: Ensure the controller can interact with `CoreTransformer` to build actual PyTorch models from the architecture definitions.
  - Task: Create unit tests for NAS framework
    Subtasks:
      - Subtask: Create `tests/test_nas_framework.py`
      - Subtask: Test `search_space` definition and accessibility.
      - Subtask: Test `NASController`'s `generate_architecture` to ensure valid architecture generation.
      - Subtask: Test `evolve_population` with dummy fitness scores to ensure new populations are generated.
Dev Notes:
Testing:
Dev Agent Record:
  - [ ] Task:
  - [ ] Subtask:
Change Log: